/**
 * Minified by js<PERSON><PERSON><PERSON><PERSON> using Terser v5.37.0.
 * Original file: /npm/three-obj-loader@1.1.3/dist/index.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
"use strict";function defaultOnError(e){throw new Error(e)}module.exports=function(e){e.OBJLoader=function(t){this.manager=void 0!==t?t:e.DefaultLoadingManager,this.materials=null,this.regexp={vertex_pattern:/^v\s+([\d|\.|\+|\-|e|E]+)\s+([\d|\.|\+|\-|e|E]+)\s+([\d|\.|\+|\-|e|E]+)/,normal_pattern:/^vn\s+([\d|\.|\+|\-|e|E]+)\s+([\d|\.|\+|\-|e|E]+)\s+([\d|\.|\+|\-|e|E]+)/,uv_pattern:/^vt\s+([\d|\.|\+|\-|e|E]+)\s+([\d|\.|\+|\-|e|E]+)/,face_vertex:/^f\s+(-?\d+)\s+(-?\d+)\s+(-?\d+)(?:\s+(-?\d+))?/,face_vertex_uv:/^f\s+(-?\d+)\/(-?\d+)\s+(-?\d+)\/(-?\d+)\s+(-?\d+)\/(-?\d+)(?:\s+(-?\d+)\/(-?\d+))?/,face_vertex_uv_normal:/^f\s+(-?\d+)\/(-?\d+)\/(-?\d+)\s+(-?\d+)\/(-?\d+)\/(-?\d+)\s+(-?\d+)\/(-?\d+)\/(-?\d+)(?:\s+(-?\d+)\/(-?\d+)\/(-?\d+))?/,face_vertex_normal:/^f\s+(-?\d+)\/\/(-?\d+)\s+(-?\d+)\/\/(-?\d+)\s+(-?\d+)\/\/(-?\d+)(?:\s+(-?\d+)\/\/(-?\d+))?/,object_pattern:/^[og]\s*(.+)?/,smoothing_pattern:/^s\s+(\d+|on|off)/,material_library_pattern:/^mtllib /,material_use_pattern:/^usemtl /}},e.OBJLoader.prototype={constructor:e.OBJLoader,load:function(t,r,s,i){var a=this;this.onError=i||defaultOnError;var n=new e.FileLoader(a.manager);n.setPath(this.path),n.load(t,(function(e){r(a.parse(e))}),s,i)},setPath:function(e){this.path=e},setMaterials:function(e){this.materials=e},_createParserState:function(){var e={objects:[],object:{},vertices:[],normals:[],uvs:[],materialLibraries:[],startObject:function(e,t){if(this.object&&!1===this.object.fromDeclaration)return this.object.name=e,void(this.object.fromDeclaration=!1!==t);var r=this.object&&"function"==typeof this.object.currentMaterial?this.object.currentMaterial():void 0;if(this.object&&"function"==typeof this.object._finalize&&this.object._finalize(!0),this.object={name:e||"",fromDeclaration:!1!==t,geometry:{vertices:[],normals:[],uvs:[]},materials:[],smooth:!0,startMaterial:function(e,t){var r=this._finalize(!1);r&&(r.inherited||r.groupCount<=0)&&this.materials.splice(r.index,1);var s={index:this.materials.length,name:e||"",mtllib:Array.isArray(t)&&t.length>0?t[t.length-1]:"",smooth:void 0!==r?r.smooth:this.smooth,groupStart:void 0!==r?r.groupEnd:0,groupEnd:-1,groupCount:-1,inherited:!1,clone:function(e){var t={index:"number"==typeof e?e:this.index,name:this.name,mtllib:this.mtllib,smooth:this.smooth,groupStart:0,groupEnd:-1,groupCount:-1,inherited:!1};return t.clone=this.clone.bind(t),t}};return this.materials.push(s),s},currentMaterial:function(){if(this.materials.length>0)return this.materials[this.materials.length-1]},_finalize:function(e){var t=this.currentMaterial();if(t&&-1===t.groupEnd&&(t.groupEnd=this.geometry.vertices.length/3,t.groupCount=t.groupEnd-t.groupStart,t.inherited=!1),e&&this.materials.length>1)for(var r=this.materials.length-1;r>=0;r--)this.materials[r].groupCount<=0&&this.materials.splice(r,1);return e&&0===this.materials.length&&this.materials.push({name:"",smooth:this.smooth}),t}},r&&r.name&&"function"==typeof r.clone){var s=r.clone(0);s.inherited=!0,this.object.materials.push(s)}this.objects.push(this.object)},finalize:function(){this.object&&"function"==typeof this.object._finalize&&this.object._finalize(!0)},parseVertexIndex:function(e,t){var r=parseInt(e,10);return 3*(r>=0?r-1:r+t/3)},parseNormalIndex:function(e,t){var r=parseInt(e,10);return 3*(r>=0?r-1:r+t/3)},parseUVIndex:function(e,t){var r=parseInt(e,10);return 2*(r>=0?r-1:r+t/2)},addVertex:function(e,t,r){var s=this.vertices,i=this.object.geometry.vertices;i.push(s[e+0]),i.push(s[e+1]),i.push(s[e+2]),i.push(s[t+0]),i.push(s[t+1]),i.push(s[t+2]),i.push(s[r+0]),i.push(s[r+1]),i.push(s[r+2])},addVertexLine:function(e){var t=this.vertices,r=this.object.geometry.vertices;r.push(t[e+0]),r.push(t[e+1]),r.push(t[e+2])},addNormal:function(e,t,r){var s=this.normals,i=this.object.geometry.normals;i.push(s[e+0]),i.push(s[e+1]),i.push(s[e+2]),i.push(s[t+0]),i.push(s[t+1]),i.push(s[t+2]),i.push(s[r+0]),i.push(s[r+1]),i.push(s[r+2])},addUV:function(e,t,r){var s=this.uvs,i=this.object.geometry.uvs;i.push(s[e+0]),i.push(s[e+1]),i.push(s[t+0]),i.push(s[t+1]),i.push(s[r+0]),i.push(s[r+1])},addUVLine:function(e){var t=this.uvs,r=this.object.geometry.uvs;r.push(t[e+0]),r.push(t[e+1])},addFace:function(e,t,r,s,i,a,n,o,h,l,d,u){var c,p=this.vertices.length,m=this.parseVertexIndex(e,p),f=this.parseVertexIndex(t,p),v=this.parseVertexIndex(r,p);if(void 0===s?this.addVertex(m,f,v):(c=this.parseVertexIndex(s,p),this.addVertex(m,f,c),this.addVertex(f,v,c)),void 0!==i){var g=this.uvs.length;m=this.parseUVIndex(i,g),f=this.parseUVIndex(a,g),v=this.parseUVIndex(n,g),void 0===s?this.addUV(m,f,v):(c=this.parseUVIndex(o,g),this.addUV(m,f,c),this.addUV(f,v,c))}if(void 0!==h){var x=this.normals.length;m=this.parseNormalIndex(h,x),f=h===l?m:this.parseNormalIndex(l,x),v=h===d?m:this.parseNormalIndex(d,x),void 0===s?this.addNormal(m,f,v):(c=this.parseNormalIndex(u,x),this.addNormal(m,f,c),this.addNormal(f,v,c))}},addLineGeometry:function(e,t){this.object.geometry.type="Line";for(var r=this.vertices.length,s=this.uvs.length,i=0,a=e.length;i<a;i++)this.addVertexLine(this.parseVertexIndex(e[i],r));var n=0;for(a=t.length;n<a;n++)this.addUVLine(this.parseUVIndex(t[n],s))}};return e.startObject("",!1),e},parse:function(t,r){void 0===r&&(r=!0),r&&console.time("OBJLoader");var s=this._createParserState();-1!==t.indexOf("\r\n")&&(t=t.replace(/\r\n/g,"\n")),-1!==t.indexOf("\\\n")&&(t=t.replace(/\\\n/g,""));for(var i=t.split("\n"),a="",n="",o="",h=[],l="function"==typeof"".trimLeft,d=0,u=i.length;d<u;d++)if(a=i[d],0!==(a=l?a.trimLeft():a.trim()).length&&"#"!==(n=a.charAt(0)))if("v"===n)" "===(o=a.charAt(1))&&null!==(h=this.regexp.vertex_pattern.exec(a))?s.vertices.push(parseFloat(h[1]),parseFloat(h[2]),parseFloat(h[3])):"n"===o&&null!==(h=this.regexp.normal_pattern.exec(a))?s.normals.push(parseFloat(h[1]),parseFloat(h[2]),parseFloat(h[3])):"t"===o&&null!==(h=this.regexp.uv_pattern.exec(a))?s.uvs.push(parseFloat(h[1]),parseFloat(h[2])):this.onError("Unexpected vertex/normal/uv line: '"+a+"'");else if("f"===n)null!==(h=this.regexp.face_vertex_uv_normal.exec(a))?s.addFace(h[1],h[4],h[7],h[10],h[2],h[5],h[8],h[11],h[3],h[6],h[9],h[12]):null!==(h=this.regexp.face_vertex_uv.exec(a))?s.addFace(h[1],h[3],h[5],h[7],h[2],h[4],h[6],h[8]):null!==(h=this.regexp.face_vertex_normal.exec(a))?s.addFace(h[1],h[3],h[5],h[7],void 0,void 0,void 0,void 0,h[2],h[4],h[6],h[8]):null!==(h=this.regexp.face_vertex.exec(a))?s.addFace(h[1],h[2],h[3],h[4]):this.onError("Unexpected face line: '"+a+"'");else if("l"===n){var c=a.substring(1).trim().split(" "),p=[],m=[];if(-1===a.indexOf("/"))p=c;else for(var f=0,v=c.length;f<v;f++){var g=c[f].split("/");""!==g[0]&&p.push(g[0]),""!==g[1]&&m.push(g[1])}s.addLineGeometry(p,m)}else if(null!==(h=this.regexp.object_pattern.exec(a))){var x=(" "+h[0].substr(1).trim()).substr(1);s.startObject(x)}else if(this.regexp.material_use_pattern.test(a))s.object.startMaterial(a.substring(7).trim(),s.materialLibraries);else if(this.regexp.material_library_pattern.test(a))s.materialLibraries.push(a.substring(7).trim());else if(null!==(h=this.regexp.smoothing_pattern.exec(a))){var b=h[1].trim().toLowerCase();s.object.smooth="1"===b||"on"===b,(A=s.object.currentMaterial())&&(A.smooth=s.object.smooth)}else{if("\0"===a)continue;this.onError("Unexpected line: '"+a+"'")}s.finalize();var _=new e.Group;_.materialLibraries=[].concat(s.materialLibraries);for(d=0,u=s.objects.length;d<u;d++){var j=s.objects[d],y=j.geometry,L=j.materials,V="Line"===y.type;if(0!==y.vertices.length){var E=new e.BufferGeometry;E.addAttribute("position",new e.BufferAttribute(new Float32Array(y.vertices),3)),y.normals.length>0?E.addAttribute("normal",new e.BufferAttribute(new Float32Array(y.normals),3)):E.computeVertexNormals(),y.uvs.length>0&&E.addAttribute("uv",new e.BufferAttribute(new Float32Array(y.uvs),2));for(var w,I=[],F=0,M=L.length;F<M;F++){var U=L[F],A=void 0;if(null!==this.materials&&(A=this.materials.create(U.name),V&&A&&!(A instanceof e.LineBasicMaterial))){var O=new e.LineBasicMaterial;O.copy(A),A=O}A||((A=V?new e.LineBasicMaterial:new e.MeshPhongMaterial).name=U.name),A.shading=U.smooth?e.SmoothShading:e.FlatShading,I.push(A)}if(I.length>1){for(F=0,M=L.length;F<M;F++){U=L[F];E.addGroup(U.groupStart,U.groupCount,F)}var B=new e.MultiMaterial(I);w=V?new e.LineSegments(E,B):new e.Mesh(E,B)}else w=V?new e.LineSegments(E,I[0]):new e.Mesh(E,I[0]);w.name=j.name,_.add(w)}}return r&&console.timeEnd("OBJLoader"),_}}};
//# sourceMappingURL=/sm/c286e4e472781793ef4b775dea4d7eb6b4c193f5dfa688225ebc61350709e984.map